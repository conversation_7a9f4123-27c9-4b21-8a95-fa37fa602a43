"""
Core API endpoints for authentication and home functionality.
This module contains the main entry points for the TagerPlus API.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
import logging

from accounts.models import CustomUser
from api.middleware import AuthMiddleware
from api.utils import generate_jwt_token
from products.models import Product
from products.home_views import _get_region_hierarchy
from products.utils import search_products_trigram
from django.core.paginator import Paginator
from django.core.cache import cache
from django.db.models import Q
from django.conf import settings

logger = logging.getLogger(__name__)

# Create router for core API endpoints
router = Router(tags=["core"])

# ============================================================================
# AUTHENTICATION SCHEMAS
# ============================================================================


class LoginRequest(Schema):
    phone: str
    password: str


class RegisterRequest(Schema):
    name: str
    password: str
    phone: str
    email: Optional[str] = None


class LoginResponse(Schema):
    success: bool
    token: str
    user_id: int
    phone: str
    is_phone_verified: bool
    wholesaler_id: Optional[int] = None


class RegisterResponse(Schema):
    success: bool
    user_id: int
    phone: str
    message: str
    token: str


class UserResponse(Schema):
    """Response schema for user data"""

    id: int
    username: str
    email: Optional[str] = None
    phone: str
    phone_verified: bool
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool
    date_joined: datetime
    wholesaler_id: Optional[int] = None


class UserUpdateRequest(Schema):
    """Request schema for updating user data"""

    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None


# ============================================================================
# HOME/DASHBOARD SCHEMAS
# ============================================================================


class CompanyOut(Schema):
    id: int
    name: str
    title: str
    slug: str


class CategoryOut(Schema):
    id: int
    name: str
    title: str
    slug: str


class ProductWithPricing(Schema):
    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str] = None
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None
    unit: str
    unit_count: float
    base_price: Optional[Decimal] = None  # lowest price or the wholesaler price
    other_price: Optional[Decimal] = None  # other prices or the highest price


class PaginatedProductResponse(Schema):
    """Response model for paginated products"""

    products: List[ProductWithPricing]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# AUTHENTICATION ENDPOINTS
# ============================================================================


@router.post("/signin", response=LoginResponse, tags=["auth"])
def signin(request, data: LoginRequest) -> LoginResponse:
    """
    User authentication endpoint
    Returns JWT token and user details including wholesaler_id if applicable
    """
    try:
        # Find user by phone
        user = CustomUser.objects.filter(phone=data.phone).first()

        if not user or not user.check_password(data.password):
            raise HttpError(401, "Invalid phone number or password")

        if not user.is_active:
            raise HttpError(401, "Account is disabled")

        # Get wholesaler ID if user has one
        try:
            wholesaler = user.wholesalers.filter(deleted_at__isnull=True).first()
            wholesaler_id = wholesaler.id if wholesaler else None
        except Exception as e:
            logger.warning(f"Error checking wholesaler for user {user.id}: {str(e)}")
            wholesaler_id = None

        # Generate JWT token
        token = generate_jwt_token(user)

        return LoginResponse(
            success=True,
            token=token,
            user_id=user.id,
            phone=user.phone,
            is_phone_verified=user.phone_verified,
            wholesaler_id=wholesaler_id,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Login error: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/signup", response=RegisterResponse, tags=["auth"])
def signup(request, data: RegisterRequest) -> RegisterResponse:
    """
    User registration endpoint
    Creates user account with phone verification set to False
    """
    try:
        # Check if user already exists
        if CustomUser.objects.filter(phone=data.phone).exists():
            raise HttpError(400, "User with this phone number already exists")

        if CustomUser.objects.filter(username=data.name).exists():
            raise HttpError(400, "Username already taken")

        # Create new user
        user = CustomUser.objects.create_user(
            username=data.name,
            email=data.email or "",
            phone=data.phone,
            password=data.password,
            phone_verified=False,  # Phone verification required separately
        )

        token = generate_jwt_token(user)

        return RegisterResponse(
            success=True,
            user_id=user.id,
            phone=user.phone,
            message="User registered successfully. Phone verification required.",
            token=token,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Registration error: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/me", response=UserResponse, tags=["auth"], auth=AuthMiddleware)
def get_current_user(request) -> UserResponse:
    """
    Get the current authenticated user's data
    Requires authentication via JWT token
    """
    try:
        # Get user from request (set by auth middleware)
        user = request.user

        if not user or not hasattr(user, "id"):
            raise HttpError(401, "Authentication required")

        # Get wholesaler ID if user has one
        try:
            wholesaler = user.wholesalers.filter(deleted_at__isnull=True).first()
            wholesaler_id = wholesaler.id if wholesaler else None
        except Exception as e:
            logger.warning(f"Error checking wholesaler for user {user.id}: {str(e)}")
            wholesaler_id = None

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            phone=user.phone,
            phone_verified=user.phone_verified,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            date_joined=user.date_joined,
            wholesaler_id=wholesaler_id,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Get user error: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/me", response=UserResponse, tags=["auth"], auth=AuthMiddleware)
def update_current_user(request, data: UserUpdateRequest) -> UserResponse:
    """
    Update the current authenticated user's data
    Requires authentication via JWT token
    """
    try:
        # Get user from request (set by auth middleware)
        user = request.user

        if not user or not hasattr(user, "id"):
            raise HttpError(401, "Authentication required")

        # Update fields if provided
        if data.first_name is not None:
            user.first_name = data.first_name

        if data.last_name is not None:
            user.last_name = data.last_name

        if data.email is not None:
            user.email = data.email

        # Handle phone number update - requires special handling
        if data.phone is not None and data.phone != user.phone:
            # Check if phone number is already in use
            if CustomUser.objects.filter(phone=data.phone).exists():
                raise HttpError(400, "Phone number already in use")

            # Update phone and mark as unverified
            user.phone = data.phone
            user.phone_verified = False

        # Save the updated user
        user.save()

        # Get wholesaler ID if user has one
        try:
            wholesaler = user.wholesalers.filter(deleted_at__isnull=True).first()
            wholesaler_id = wholesaler.id if wholesaler else None
        except Exception as e:
            logger.warning(f"Error checking wholesaler for user {user.id}: {str(e)}")
            wholesaler_id = None

        # Return the updated user data
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            phone=user.phone,
            phone_verified=user.phone_verified,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            date_joined=user.date_joined,
            wholesaler_id=wholesaler_id,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Update user error: {str(e)}")
        raise HttpError(500, "Internal server error")


# ============================================================================
# HOME/DASHBOARD ENDPOINTS
# ============================================================================


@router.get(
    "/home/<USER>",
    response=PaginatedProductResponse,
    tags=["home"],
    auth=AuthMiddleware,
)
def get_home_products(
    request,
    page: int = 1,
    page_size: int = 20,
    region_id: Optional[int] = None,
    wholesaler_id: Optional[int] = None,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    company_id: Optional[int] = None,
) -> PaginatedProductResponse:
    """
    Get products for home page with pagination and filtering support.

    Query Parameters:
    - page: Page number (default: 1)
    - page_size: Number of items per page (default: 20, max: 100)
    - region_id: Filter by region ID (includes parent regions)
    - wholesaler_id: Filter by specific wholesaler ID
    - search: Search term for product name/title/description
    - category_id: Filter by category ID
    - company_id: Filter by company ID
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build base queryset
        queryset = Product.objects.filter(deleted_at__isnull=True).select_related(
            "company", "category"
        )

        # Apply filters
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        if company_id:
            queryset = queryset.filter(company_id=company_id)

        # Apply search
        if search:
            queryset = search_products_trigram(search, queryset)

        # Apply region filtering
        if region_id:
            region_ids = _get_region_hierarchy(region_id)
            queryset = queryset.filter(
                wholesalers__deleted_at__isnull=True,
                wholesalers__inventory_count__gt=0,
                wholesalers__wholesaler__region_min_charge__region_id__in=region_ids,
                wholesalers__wholesaler__region_min_charge__deleted_at__isnull=True,
            ).distinct()

        # Apply wholesaler filtering
        if wholesaler_id:
            queryset = queryset.filter(
                wholesalers__wholesaler_id=wholesaler_id,
                wholesalers__deleted_at__isnull=True,
                wholesalers__inventory_count__gt=0,
            ).distinct()

        # Paginate results
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        products = []
        for product in page_obj.object_list:
            # Get pricing information
            base_price = None
            other_price = None

            # Get items for this product
            items = product.wholesalers.filter(
                deleted_at__isnull=True, inventory_count__gt=0
            ).order_by("base_price")

            if items.exists():
                base_price = items.first().base_price
                if items.count() > 1:
                    other_price = items.last().base_price

            products.append(
                ProductWithPricing(
                    id=product.id,
                    name=product.name,
                    title=product.title,
                    barcode=product.barcode,
                    slug=product.slug,
                    description=product.description,
                    image_url=product.image.url if product.image else None,
                    company_id=product.company.id if product.company else None,
                    category_id=product.category.id if product.category else None,
                    company=CompanyOut(
                        id=product.company.id,
                        name=product.company.name,
                        title=product.company.title,
                        slug=product.company.slug,
                    )
                    if product.company
                    else None,
                    category=CategoryOut(
                        id=product.category.id,
                        name=product.category.name,
                        title=product.category.title,
                        slug=product.category.slug,
                    )
                    if product.category
                    else None,
                    unit=product.unit,
                    unit_count=float(product.unit_count),
                    base_price=base_price,
                    other_price=other_price,
                )
            )

        return PaginatedProductResponse(
            products=products,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error getting home products: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/", tags=["core"])
def api_root(request):
    """API root endpoint"""
    return {"message": "TagerPlus API v2", "status": "active"}
