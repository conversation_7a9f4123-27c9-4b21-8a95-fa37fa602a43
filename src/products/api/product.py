"""
CRUD API endpoints for Product model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib.postgres.search import SearchQuery
import logging

from products.models import Product, Company, Category, MeasurementUnit, Region
from products.utils import search_products_trigram
from products.home_views import _get_region_hierarchy
from wholesalers.models import Item, RegionMinCharge, Wholesaler
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Product endpoints
router = Router(tags=["products"])

# ============================================================================
# SCHEMAS
# ============================================================================


class ProductIn(Schema):
    """Schema for creating a new product"""

    name: str
    title: str
    barcode: str
    slug: str
    description: str = ""
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    unit: str = MeasurementUnit.PIECE
    unit_count: Decimal = Decimal("1.0")


class ProductUpdate(Schema):
    """Schema for updating product data"""

    name: Optional[str] = None
    title: Optional[str] = None
    barcode: Optional[str] = None
    slug: Optional[str] = None
    description: Optional[str] = None
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    unit: Optional[str] = None
    unit_count: Optional[Decimal] = None


class CompanyOut(Schema):
    """Simplified company schema for product responses"""

    id: int
    name: str
    title: str
    slug: str


class CategoryOut(Schema):
    """Simplified category schema for product responses"""

    id: int
    name: str
    title: str
    slug: str


class ProductOut(Schema):
    """Schema for product output"""

    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str] = None
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None
    unit: str
    unit_count: float
    items_count: int
    created_at: datetime
    updated_at: datetime


class PaginatedProductResponse(Schema):
    """Paginated response for products"""

    products: List[ProductOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# SCHEMAS FOR PRODUCT WITH WHOLESALER ITEMS ENDPOINT
# ============================================================================


class RegionFilterIn(Schema):
    """Schema for region filter input"""

    region_id: int


class WholesalerItemOut(Schema):
    """Schema for wholesaler item output"""

    id: int
    base_price: Decimal
    inventory_count: int
    minimum_order_quantity: int
    maximum_order_quantity: Optional[int] = None
    price_expiry: datetime
    expires_at: datetime
    created_at: datetime
    updated_at: datetime


class WholesalerDetailOut(Schema):
    """Schema for detailed wholesaler information"""

    id: int
    category: str
    title: str
    username: str
    logo_url: Optional[str] = None
    background_image_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class RegionMinChargeOut(Schema):
    """Schema for regional minimum charge information"""

    id: int
    min_charge: Decimal
    min_items: int
    region_id: int
    region_name: str


class WholesalerItemWithDetailsOut(Schema):
    """Schema combining wholesaler item with wholesaler details and regional pricing"""

    item: WholesalerItemOut
    wholesaler: WholesalerDetailOut
    regional_pricing: Optional[RegionMinChargeOut] = None


class ProductWithWholesalerItemsOut(Schema):
    """Schema for product with all associated wholesaler items"""

    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str] = None
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None
    unit: str
    unit_count: float
    items_count: int
    created_at: datetime
    updated_at: datetime
    wholesaler_items: List[WholesalerItemWithDetailsOut]


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedProductResponse, auth=AuthMiddleware)
def list_products(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    company_id: Optional[int] = None,
    category_id: Optional[int] = None,
    unit: Optional[str] = None,
) -> PaginatedProductResponse:
    """
    List all products with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Product.objects.filter(deleted_at__isnull=True).select_related(
            "company", "category"
        )

        # Apply filters
        if company_id:
            queryset = queryset.filter(company_id=company_id)

        if category_id:
            queryset = queryset.filter(category_id=category_id)

        if unit:
            queryset = queryset.filter(unit=unit)

        # Apply search
        if search:
            queryset = search_products_trigram(search, queryset)

        # Order by creation date (newest first)
        queryset = queryset.order_by("-created_at")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        products = []
        for product in page_obj.object_list:
            products.append(
                ProductOut(
                    id=product.id,
                    name=product.name,
                    title=product.title,
                    barcode=product.barcode,
                    slug=product.slug,
                    description=product.description,
                    image_url=product.image.url if product.image else None,
                    company=CompanyOut(
                        id=product.company.id,
                        name=product.company.name,
                        title=product.company.title,
                        slug=product.company.slug,
                    )
                    if product.company
                    else None,
                    category=CategoryOut(
                        id=product.category.id,
                        name=product.category.name,
                        title=product.category.title,
                        slug=product.category.slug,
                    )
                    if product.category
                    else None,
                    unit=product.unit,
                    unit_count=float(product.unit_count),
                    items_count=product.items_count,
                    created_at=product.created_at,
                    updated_at=product.updated_at,
                )
            )

        return PaginatedProductResponse(
            products=products,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing products: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{product_id}", response=ProductOut, auth=AuthMiddleware)
def get_product(request, product_id: int) -> ProductOut:
    """
    Get a specific product by ID.
    Requires authentication.
    """
    try:
        product = get_object_or_404(
            Product.objects.select_related("company", "category"),
            id=product_id,
            deleted_at__isnull=True,
        )

        return ProductOut(
            id=product.id,
            name=product.name,
            title=product.title,
            barcode=product.barcode,
            slug=product.slug,
            description=product.description,
            image_url=product.image.url if product.image else None,
            company=CompanyOut(
                id=product.company.id,
                name=product.company.name,
                title=product.company.title,
                slug=product.company.slug,
            )
            if product.company
            else None,
            category=CategoryOut(
                id=product.category.id,
                name=product.category.name,
                title=product.category.title,
                slug=product.category.slug,
            )
            if product.category
            else None,
            unit=product.unit,
            unit_count=float(product.unit_count),
            items_count=product.items_count,
            created_at=product.created_at,
            updated_at=product.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting product {product_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=ProductOut, auth=AuthMiddleware)
def create_product(request, data: ProductIn) -> ProductOut:
    """
    Create a new product.
    Requires authentication.
    """
    try:
        # Check if barcode already exists
        if Product.objects.filter(barcode=data.barcode).exists():
            raise HttpError(400, "Product with this barcode already exists")

        # Check if slug already exists
        if Product.objects.filter(slug=data.slug).exists():
            raise HttpError(400, "Product with this slug already exists")

        # Validate company if provided
        company = None
        if data.company_id:
            company = get_object_or_404(
                Company, id=data.company_id, deleted_at__isnull=True
            )

        # Validate category if provided
        category = None
        if data.category_id:
            category = get_object_or_404(
                Category, id=data.category_id, deleted_at__isnull=True
            )

        # Validate unit
        if data.unit not in [choice.value for choice in MeasurementUnit]:
            raise HttpError(
                400,
                f"Invalid unit. Must be one of: {[choice.value for choice in MeasurementUnit]}",
            )

        # Create product
        product = Product.objects.create(
            name=data.name,
            title=data.title,
            barcode=data.barcode,
            slug=data.slug,
            description=data.description,
            company=company,
            category=category,
            unit=data.unit,
            unit_count=data.unit_count,
        )

        return ProductOut(
            id=product.id,
            name=product.name,
            title=product.title,
            barcode=product.barcode,
            slug=product.slug,
            description=product.description,
            image_url=product.image.url if product.image else None,
            company=CompanyOut(
                id=product.company.id,
                name=product.company.name,
                title=product.company.title,
                slug=product.company.slug,
            )
            if product.company
            else None,
            category=CategoryOut(
                id=product.category.id,
                name=product.category.name,
                title=product.category.title,
                slug=product.category.slug,
            )
            if product.category
            else None,
            unit=product.unit,
            unit_count=product.unit_count,
            items_count=product.items_count,
            created_at=product.created_at,
            updated_at=product.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating product: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{product_id}", response=ProductOut, auth=AuthMiddleware)
def update_product(request, product_id: int, data: ProductUpdate) -> ProductOut:
    """
    Update a product.
    Requires authentication.
    """
    try:
        product = get_object_or_404(
            Product.objects.select_related("company", "category"),
            id=product_id,
            deleted_at__isnull=True,
        )

        # Update fields if provided
        if data.name is not None:
            product.name = data.name

        if data.title is not None:
            product.title = data.title

        if data.barcode is not None:
            if (
                Product.objects.filter(barcode=data.barcode)
                .exclude(id=product_id)
                .exists()
            ):
                raise HttpError(400, "Product with this barcode already exists")
            product.barcode = data.barcode

        if data.slug is not None:
            if Product.objects.filter(slug=data.slug).exclude(id=product_id).exists():
                raise HttpError(400, "Product with this slug already exists")
            product.slug = data.slug

        if data.description is not None:
            product.description = data.description

        if data.company_id is not None:
            if data.company_id == 0:
                product.company = None
            else:
                company = get_object_or_404(
                    Company, id=data.company_id, deleted_at__isnull=True
                )
                product.company = company

        if data.category_id is not None:
            if data.category_id == 0:
                product.category = None
            else:
                category = get_object_or_404(
                    Category, id=data.category_id, deleted_at__isnull=True
                )
                product.category = category

        if data.unit is not None:
            if data.unit not in [choice.value for choice in MeasurementUnit]:
                raise HttpError(
                    400,
                    f"Invalid unit. Must be one of: {[choice.value for choice in MeasurementUnit]}",
                )
            product.unit = data.unit

        if data.unit_count is not None:
            product.unit_count = data.unit_count

        product.save()

        return ProductOut(
            id=product.id,
            name=product.name,
            title=product.title,
            barcode=product.barcode,
            slug=product.slug,
            description=product.description,
            image_url=product.image.url if product.image else None,
            company=CompanyOut(
                id=product.company.id,
                name=product.company.name,
                title=product.company.title,
                slug=product.company.slug,
            )
            if product.company
            else None,
            category=CategoryOut(
                id=product.category.id,
                name=product.category.name,
                title=product.category.title,
                slug=product.category.slug,
            )
            if product.category
            else None,
            unit=product.unit,
            unit_count=product.unit_count,
            items_count=product.items_count,
            created_at=product.created_at,
            updated_at=product.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating product {product_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{product_id}", auth=AuthMiddleware)
def delete_product(request, product_id: int):
    """
    Soft delete a product.
    Requires authentication.
    """
    try:
        product = get_object_or_404(Product, id=product_id, deleted_at__isnull=True)

        # Perform soft delete
        product.delete()

        return {"message": "Product deleted successfully"}

    except Exception as e:
        logger.exception(f"Error deleting product {product_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


# ============================================================================
# PRODUCT WITH WHOLESALER ITEMS ENDPOINT
# ============================================================================


@router.post(
    "/{product_id}/wholesaler-items",
    response=ProductWithWholesalerItemsOut,
    auth=AuthMiddleware,
)
def get_product_with_wholesaler_items(
    request, product_id: int, data: RegionFilterIn
) -> ProductWithWholesalerItemsOut:
    """
    Get a product by its ID and return all wholesaler items associated with that product.
    Filters by region hierarchy to show only relevant wholesalers and their regional pricing.

    Args:
        product_id: The ID of the product to retrieve
        data: Request body containing region_id for filtering

    Returns:
        ProductWithWholesalerItemsOut: Product details with all associated wholesaler items
    """
    try:
        # Get the product with related company and category data
        product = get_object_or_404(
            Product.objects.select_related("company", "category"),
            id=product_id,
            deleted_at__isnull=True,
        )

        # Get region hierarchy for filtering
        region_ids = _get_region_hierarchy(data.region_id)
        if not region_ids:
            raise HttpError(400, "Invalid region_id provided")

        # Get all items for this product from wholesalers that serve the region hierarchy
        items_query = (
            Item.objects.filter(
                product=product,
                deleted_at__isnull=True,
                wholesaler__deleted_at__isnull=True,
                # Filter by wholesalers that have regional pricing for the region hierarchy
                wholesaler__region_min_charge__region_id__in=region_ids,
                wholesaler__region_min_charge__deleted_at__isnull=True,
            )
            .select_related("wholesaler")
            .prefetch_related("wholesaler__region_min_charge__region")
            .distinct()
        )

        # Build wholesaler items with details
        wholesaler_items = []
        for item in items_query:
            # Get regional pricing for the specific region
            regional_pricing = None
            region_min_charge = (
                item.wholesaler.region_min_charge.filter(
                    region_id__in=region_ids, deleted_at__isnull=True
                )
                .select_related("region")
                .first()
            )

            if region_min_charge:
                regional_pricing = RegionMinChargeOut(
                    id=region_min_charge.id,
                    min_charge=region_min_charge.min_charge,
                    min_items=region_min_charge.min_items,
                    region_id=region_min_charge.region.id,
                    region_name=region_min_charge.region.name,
                )

            # Build wholesaler item with details
            wholesaler_item = WholesalerItemWithDetailsOut(
                item=WholesalerItemOut(
                    id=item.id,
                    base_price=item.base_price,
                    inventory_count=item.inventory_count,
                    minimum_order_quantity=item.minimum_order_quantity,
                    maximum_order_quantity=item.maximum_order_quantity,
                    price_expiry=item.price_expiry,
                    expires_at=item.expires_at,
                    created_at=item.created_at,
                    updated_at=item.updated_at,
                ),
                wholesaler=WholesalerDetailOut(
                    id=item.wholesaler.id,
                    category=item.wholesaler.category,
                    title=item.wholesaler.title,
                    username=item.wholesaler.username,
                    logo_url=item.wholesaler.logo.url if item.wholesaler.logo else None,
                    background_image_url=item.wholesaler.background_image.url
                    if item.wholesaler.background_image
                    else None,
                    created_at=item.wholesaler.created_at,
                    updated_at=item.wholesaler.updated_at,
                ),
                regional_pricing=regional_pricing,
            )
            wholesaler_items.append(wholesaler_item)

        # Build the response
        return ProductWithWholesalerItemsOut(
            id=product.id,
            name=product.name,
            title=product.title,
            barcode=product.barcode,
            slug=product.slug,
            description=product.description,
            image_url=product.image.url if product.image else None,
            company=CompanyOut(
                id=product.company.id,
                name=product.company.name,
                title=product.company.title,
                slug=product.company.slug,
            )
            if product.company
            else None,
            category=CategoryOut(
                id=product.category.id,
                name=product.category.name,
                title=product.category.title,
                slug=product.category.slug,
            )
            if product.category
            else None,
            unit=product.unit,
            unit_count=product.unit_count,
            items_count=product.items_count,
            created_at=product.created_at,
            updated_at=product.updated_at,
            wholesaler_items=wholesaler_items,
        )

    except Exception as e:
        logger.exception(
            f"Error getting product {product_id} with wholesaler items: {str(e)}"
        )
        raise HttpError(500, "Internal server error")
