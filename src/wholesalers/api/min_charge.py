import logging

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from django.core.paginator import Paginator

from wholesalers.models import RegionMinCharge
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Wholesaler endpoints
router = Router(tags=["wholesalers"])

# ============================================================================
# SCHEMAS
# ============================================================================


class RegionMinChargeOut(Schema):
    """Schema for RegionMinCharge output"""

    id: int
    min_charge: float
    min_items: int
    created_at: datetime
    updated_at: datetime


@router.get("/", response=List[RegionMinChargeOut], auth=AuthMiddleware)
def list_min_charges(
    request, region_id: int, wholesaler_id: int
) -> List[RegionMinChargeOut]:
    """
    List all min charges.
    Requires authentication.
    """
    try:
        # Build queryset
        queryset = RegionMinCharge.objects.filter(
            deleted_at__isnull=True, region_id=region_id, wholesaler_id=wholesaler_id
        )

        # Get page number and page size from request
        page = request.GET.get("page", 1)
        page_size = request.GET.get("page_size", 20)

        # Paginate queryset
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Serialize queryset
        min_charges = [
            RegionMinChargeOut(
                id=charge.id,
                min_charge=float(charge.min_charge),
                min_items=charge.min_items,
                created_at=charge.created_at,
                updated_at=charge.updated_at,
            )
            for charge in page_obj
        ]

        # Return response
        return min_charges
    except Exception as e:
        logger.exception(f"Error listing min charges: {str(e)}")
        raise HttpError(500, "Internal server error")
