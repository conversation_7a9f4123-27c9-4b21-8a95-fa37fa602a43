"""
CRUD API endpoints for Order model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from stores.models import Order, OrderStatus, Store
from wholesalers.models import Wholesaler
from accounts.models import CustomUser
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Order endpoints
router = Router(tags=["orders"])

# ============================================================================
# SCHEMAS
# ============================================================================


class OrderIn(Schema):
    """Schema for creating a new order"""

    wholesaler_id: int
    store_id: int
    total_price: Decimal
    fees: Decimal
    products_total_price: Decimal
    products_total_quantity: int
    deliver_at: Optional[datetime] = None


class OrderUpdate(Schema):
    """Schema for updating order data"""

    status: Optional[str] = None
    status_reason: Optional[str] = None
    deliver_at: Optional[datetime] = None
    final_completed_price: Optional[Decimal] = None


class StoreOrderOut(Schema):
    """Simplified store schema for order responses"""

    id: int
    name: str
    address: str


class WholesalerOut(Schema):
    """Simplified wholesaler schema for order responses"""

    id: int
    title: str


class OrderOut(Schema):
    """Schema for order output"""

    id: int
    wholesaler: WholesalerOut
    store: StoreOrderOut
    total_price: Decimal
    fees: Decimal
    deliver_at: Optional[datetime] = None
    products_total_price: Decimal
    products_total_quantity: int
    final_completed_price: Optional[Decimal] = None
    completed_at: Optional[datetime] = None
    status: str
    status_reason: Optional[str] = None
    status_updated_at: datetime
    created_at: datetime
    updated_at: datetime


class PaginatedOrderResponse(Schema):
    """Paginated response for orders"""

    orders: List[OrderOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# HELPER FUNCTIONS
# ============================================================================


def _build_order_response(order) -> OrderOut:
    """Helper function to build OrderOut response"""
    return OrderOut(
        id=order.id,
        wholesaler=WholesalerOut(
            id=order.wholesaler.id,
            title=order.wholesaler.title,
        ),
        store=StoreOrderOut(
            id=order.store.id,
            name=order.store.name,
            address=order.store.address,
        ),
        total_price=order.total_price,
        fees=order.fees,
        deliver_at=order.deliver_at,
        products_total_price=order.products_total_price,
        products_total_quantity=order.products_total_quantity,
        final_completed_price=order.final_completed_price,
        completed_at=order.completed_at,
        status=order.status,
        status_reason=order.status_reason,
        status_updated_at=order.status_updated_at,
        created_at=order.created_at,
        updated_at=order.updated_at,
    )


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedOrderResponse, auth=AuthMiddleware)
def list_orders(
    request,
    page: int = 1,
    page_size: int = 20,
    status: Optional[str] = None,
    store_id: Optional[int] = None,
    wholesaler_id: Optional[int] = None,
) -> PaginatedOrderResponse:
    """
    List all orders with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Order.objects.filter(deleted_at__isnull=True).select_related(
            "wholesaler", "store"
        )

        # Apply filters
        if status:
            queryset = queryset.filter(status=status)

        if store_id:
            queryset = queryset.filter(store_id=store_id)

        if wholesaler_id:
            queryset = queryset.filter(wholesaler_id=wholesaler_id)

        # Order by creation date (newest first)
        queryset = queryset.order_by("-created_at")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        orders = [_build_order_response(order) for order in page_obj.object_list]

        return PaginatedOrderResponse(
            orders=orders,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing orders: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{order_id}", response=OrderOut, auth=AuthMiddleware)
def get_order(request, order_id: int) -> OrderOut:
    """
    Get a specific order by ID.
    Requires authentication.
    """
    try:
        order = get_object_or_404(
            Order.objects.select_related("wholesaler", "store"),
            id=order_id,
            deleted_at__isnull=True,
        )

        return _build_order_response(order)

    except Exception as e:
        logger.exception(f"Error getting order {order_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=OrderOut, auth=AuthMiddleware)
def create_order(request, data: OrderIn) -> OrderOut:
    """
    Create a new order.
    Requires authentication.
    """
    try:
        # Check if wholesaler exists
        wholesaler = get_object_or_404(
            Wholesaler, id=data.wholesaler_id, deleted_at__isnull=True
        )

        # Check if store exists
        store = get_object_or_404(Store, id=data.store_id, deleted_at__isnull=True)

        # Create order
        order = Order.objects.create(
            wholesaler=wholesaler,
            store=store,
            total_price=data.total_price,
            fees=data.fees,
            products_total_price=data.products_total_price,
            products_total_quantity=data.products_total_quantity,
            deliver_at=data.deliver_at,
            status=OrderStatus.PENDING,
        )

        return _build_order_response(order)

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating order: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{order_id}", response=OrderOut, auth=AuthMiddleware)
def update_order(request, order_id: int, data: OrderUpdate) -> OrderOut:
    """
    Update an order.
    Requires authentication.
    """
    try:
        order = get_object_or_404(
            Order.objects.select_related("wholesaler", "store"),
            id=order_id,
            deleted_at__isnull=True,
        )

        # Update fields if provided
        if data.status is not None:
            if data.status not in [choice.value for choice in OrderStatus]:
                raise HttpError(
                    400,
                    f"Invalid status. Must be one of: {[choice.value for choice in OrderStatus]}",
                )
            order.status = data.status
            order.status_updated_by = request.user

        if data.status_reason is not None:
            order.status_reason = data.status_reason

        if data.deliver_at is not None:
            order.deliver_at = data.deliver_at

        if data.final_completed_price is not None:
            order.final_completed_price = data.final_completed_price

        order.save()

        return _build_order_response(order)

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating order {order_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{order_id}", auth=AuthMiddleware)
def delete_order(request, order_id: int):
    """
    Soft delete an order.
    Requires authentication.
    """
    try:
        order = get_object_or_404(Order, id=order_id, deleted_at__isnull=True)

        # Perform soft delete
        order.delete()

        return {"message": "Order deleted successfully"}

    except Exception as e:
        logger.exception(f"Error deleting order {order_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
