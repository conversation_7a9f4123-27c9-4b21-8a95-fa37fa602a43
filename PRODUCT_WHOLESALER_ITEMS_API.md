# Product with Wholesaler Items API Endpoint

## Overview

This document describes the new API endpoint that retrieves a product by its ID and returns all wholesaler items associated with that product, filtered by regional pricing.

## Endpoint Details

**URL:** `POST /api/products/{product_id}/wholesaler-items`

**Method:** `POST`

**Authentication:** Required (<PERSON><PERSON><PERSON> Bearer token)

**Content-Type:** `application/json`

## URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `product_id` | integer | Yes | The ID of the product to retrieve |

## Request Body

```json
{
  "region_id": 1
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `region_id` | integer | Yes | The ID of the region to filter regional pricing |

## Response

### Success Response (200 OK)

```json
{
  "id": 1,
  "name": "Product Name",
  "title": "Product Title",
  "barcode": "1234567890123",
  "slug": "product-slug",
  "description": "Product description",
  "image_url": "https://example.com/image.jpg",
  "company": {
    "id": 1,
    "name": "Company Name",
    "title": "Company Title",
    "slug": "company-slug"
  },
  "category": {
    "id": 1,
    "name": "Category Name",
    "title": "Category Title",
    "slug": "category-slug"
  },
  "unit": "PIECE",
  "unit_count": "1.00",
  "items_count": 2,
  "created_at": "2024-08-26T00:00:00Z",
  "updated_at": "2024-08-26T00:00:00Z",
  "wholesaler_items": [
    {
      "item": {
        "id": 1,
        "base_price": "25.50",
        "inventory_count": 100,
        "minimum_order_quantity": 1,
        "maximum_order_quantity": 50,
        "price_expiry": "2024-08-29T00:00:00Z",
        "expires_at": "2024-08-29T00:00:00Z",
        "created_at": "2024-08-26T00:00:00Z",
        "updated_at": "2024-08-26T00:00:00Z"
      },
      "wholesaler": {
        "id": 1,
        "category": "GROCERY",
        "title": "Wholesaler Name",
        "username": "wholesaler_username",
        "logo_url": "https://example.com/logo.jpg",
        "background_image_url": "https://example.com/bg.jpg",
        "created_at": "2024-08-26T00:00:00Z",
        "updated_at": "2024-08-26T00:00:00Z"
      },
      "regional_pricing": {
        "id": 1,
        "min_charge": "100.00",
        "min_items": 5,
        "region_id": 1,
        "region_name": "Region Name"
      }
    }
  ]
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "detail": "Invalid region_id provided"
}
```

#### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

#### 404 Not Found
```json
{
  "detail": "Not found."
}
```

#### 422 Unprocessable Entity
```json
{
  "detail": [
    {
      "type": "missing",
      "loc": ["body", "region_id"],
      "msg": "Field required"
    }
  ]
}
```

#### 500 Internal Server Error
```json
{
  "detail": "Internal server error"
}
```

## Response Fields Description

### Product Fields
- `id`: Product unique identifier
- `name`: Product name
- `title`: Product display title
- `barcode`: Product barcode
- `slug`: URL-friendly product identifier
- `description`: Product description
- `image_url`: URL to product image (null if no image)
- `unit`: Measurement unit (e.g., "PIECE", "KG", "LITER")
- `unit_count`: Number of units in this product
- `items_count`: Total number of wholesaler items for this product
- `created_at`: Product creation timestamp
- `updated_at`: Product last update timestamp

### Company Fields (nullable)
- `id`: Company unique identifier
- `name`: Company name
- `title`: Company display title
- `slug`: URL-friendly company identifier

### Category Fields (nullable)
- `id`: Category unique identifier
- `name`: Category name
- `title`: Category display title
- `slug`: URL-friendly category identifier

### Wholesaler Item Fields
- `id`: Item unique identifier
- `base_price`: Base price for the item
- `inventory_count`: Current inventory count
- `minimum_order_quantity`: Minimum quantity that can be ordered
- `maximum_order_quantity`: Maximum quantity that can be ordered (nullable)
- `price_expiry`: Price expiration timestamp
- `expires_at`: Item expiration timestamp
- `created_at`: Item creation timestamp
- `updated_at`: Item last update timestamp

### Wholesaler Fields
- `id`: Wholesaler unique identifier
- `category`: Wholesaler category (e.g., "GROCERY", "PHARMACEUTICAL")
- `title`: Wholesaler display name
- `username`: Wholesaler username
- `logo_url`: URL to wholesaler logo (nullable)
- `background_image_url`: URL to wholesaler background image (nullable)
- `created_at`: Wholesaler creation timestamp
- `updated_at`: Wholesaler last update timestamp

### Regional Pricing Fields (nullable)
- `id`: Regional pricing unique identifier
- `min_charge`: Minimum charge for orders in this region
- `min_items`: Minimum number of items for orders in this region
- `region_id`: Region unique identifier
- `region_name`: Region display name

## Regional Hierarchy

The endpoint uses regional hierarchy to filter wholesalers. When you provide a `region_id`, the system will:

1. Find the specified region
2. Include all parent regions in the hierarchy (e.g., if you specify a city, it will also include the state and country)
3. Return wholesaler items from wholesalers that serve any region in this hierarchy

This allows for flexible regional coverage where wholesalers can serve at different geographic levels.

## Usage Examples

### Get product with wholesaler items for a specific region
```bash
curl -X POST "http://localhost:8000/api/products/1/wholesaler-items" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"region_id": 1}'
```

### Using with JavaScript/Fetch
```javascript
const response = await fetch('/api/products/1/wholesaler-items', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    region_id: 1
  })
});

const data = await response.json();
```

## Integration Notes

- This endpoint follows the existing Django Ninja API patterns used throughout the TagerPlus application
- Authentication is handled via the existing JWT middleware
- Error handling follows the standard HTTP status code conventions
- The endpoint is registered under the products router at `/api/products/`
- Soft deletes are respected (deleted items/wholesalers/regions are excluded)
- Database queries are optimized with `select_related` and `prefetch_related` for performance
