### Product with Wholesaler Items API Test
# This file contains HTTP requests to test the new product wholesaler items endpoint

@baseUrl = http://localhost:8000/api
@authToken = YOUR_JWT_TOKEN_HERE

### Test 1: Get product with wholesaler items (successful case)
POST {{baseUrl}}/products/1/wholesaler-items
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "region_id": 1
}

### Test 2: Get product with wholesaler items for different region
POST {{baseUrl}}/products/1/wholesaler-items
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "region_id": 2
}

### Test 3: Test with invalid product ID (should return 404)
POST {{baseUrl}}/products/99999/wholesaler-items
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "region_id": 1
}

### Test 4: Test with invalid region ID (should return 400)
POST {{baseUrl}}/products/1/wholesaler-items
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "region_id": 99999
}

### Test 5: Test without authentication (should return 401)
POST {{baseUrl}}/products/1/wholesaler-items
Content-Type: application/json

{
  "region_id": 1
}

### Test 6: Test with missing region_id (should return 422)
POST {{baseUrl}}/products/1/wholesaler-items
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
}

### Test 7: Test with invalid region_id type (should return 422)
POST {{baseUrl}}/products/1/wholesaler-items
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "region_id": "invalid"
}

###
# Expected Response Structure (for successful requests):
# {
#   "id": 1,
#   "name": "Product Name",
#   "title": "Product Title",
#   "barcode": "1234567890123",
#   "slug": "product-slug",
#   "description": "Product description",
#   "image_url": "https://example.com/image.jpg",
#   "company": {
#     "id": 1,
#     "name": "Company Name",
#     "title": "Company Title",
#     "slug": "company-slug"
#   },
#   "category": {
#     "id": 1,
#     "name": "Category Name",
#     "title": "Category Title",
#     "slug": "category-slug"
#   },
#   "unit": "PIECE",
#   "unit_count": "1.00",
#   "items_count": 2,
#   "created_at": "2024-08-26T00:00:00Z",
#   "updated_at": "2024-08-26T00:00:00Z",
#   "wholesaler_items": [
#     {
#       "item": {
#         "id": 1,
#         "base_price": "25.50",
#         "inventory_count": 100,
#         "minimum_order_quantity": 1,
#         "maximum_order_quantity": 50,
#         "price_expiry": "2024-08-29T00:00:00Z",
#         "expires_at": "2024-08-29T00:00:00Z",
#         "created_at": "2024-08-26T00:00:00Z",
#         "updated_at": "2024-08-26T00:00:00Z"
#       },
#       "wholesaler": {
#         "id": 1,
#         "category": "GROCERY",
#         "title": "Wholesaler Name",
#         "username": "wholesaler_username",
#         "logo_url": "https://example.com/logo.jpg",
#         "background_image_url": "https://example.com/bg.jpg",
#         "created_at": "2024-08-26T00:00:00Z",
#         "updated_at": "2024-08-26T00:00:00Z"
#       },
#       "regional_pricing": {
#         "id": 1,
#         "min_charge": "100.00",
#         "min_items": 5,
#         "region_id": 1,
#         "region_name": "Region Name"
#       }
#     }
#   ]
# }
